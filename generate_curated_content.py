    #!/usr/bin/env python3
"""
Script to generate curated content with themes, content sets, and task items.

This script generates:
- 15-20 themes (like "भूगोल र वातावरण")
- 2 content sets per theme
- 5 questions per set (mix of single choice and multiple choice)

Database: mongodb://localhost:27017/
"""

import asyncio
import random
from datetime import datetime, timezone
from bson import ObjectId
from pymongo import AsyncMongoClient
from typing import List, Dict, Any
import uuid

# Database configuration
DB_URL = "mongodb://localhost:27017/"
DB_NAME = "nepali_app_tenant_1"  # Default tenant database
USER_ID = ObjectId("68391d86b8b0e7ec9ababfbb")  # Default user ID

class CuratedContentGenerator:
    def __init__(self, db_url: str, db_name: str):
        self.client = AsyncMongoClient(db_url)
        self.db = self.client[db_name]
        
    async def close(self):
        """Close database connection."""
        self.client.close()
    
    def generate_themes(self) -> List[Dict[str, Any]]:
        """Generate 15-20 themes with Nepali content."""
        themes = [
            {
                "name": "भूगोल र वातावरण",
                "name_en": "Geography & Environment",
                "description": "नेपालको भौगोलिक संरचना र वातावरणीय विविधता",
                "description_en": "Nepal's geographical structure and environmental diversity",
                "icon": "🏔️",
                "color": "#2E8B57",
                "category": "भूगोल"
            },
            {
                "name": "नेपाली संस्कृति",
                "name_en": "Nepali Culture",
                "description": "नेपाली संस्कृति र परम्पराका बारेमा",
                "description_en": "About Nepali culture and traditions",
                "icon": "🏛️",
                "color": "#FF6B6B",
                "category": "संस्कृति"
            },
            {
                "name": "इतिहास र विरासत",
                "name_en": "History & Heritage",
                "description": "नेपालको ऐतिहासिक घटनाहरू र सांस्कृतिक विरासत",
                "description_en": "Nepal's historical events and cultural heritage",
                "icon": "📜",
                "color": "#8B4513",
                "category": "इतिहास"
            },
            {
                "name": "भाषा र साहित्य",
                "name_en": "Language & Literature",
                "description": "नेपाली भाषा र साहित्यका विविध पक्षहरू",
                "description_en": "Various aspects of Nepali language and literature",
                "icon": "📚",
                "color": "#4169E1",
                "category": "भाषा"
            },
            {
                "name": "विज्ञान र प्रविधि",
                "name_en": "Science & Technology",
                "description": "आधुनिक विज्ञान र प्रविधिका बारेमा",
                "description_en": "About modern science and technology",
                "icon": "🔬",
                "color": "#00CED1",
                "category": "विज्ञान"
            },
            {
                "name": "खेलकुद",
                "name_en": "Sports",
                "description": "नेपाली र अन्तर्राष्ट्रिय खेलकुदका बारेमा",
                "description_en": "About Nepali and international sports",
                "icon": "⚽",
                "color": "#32CD32",
                "category": "खेलकुद"
            },
            {
                "name": "राजनीति र शासन",
                "name_en": "Politics & Governance",
                "description": "नेपालको राजनीतिक प्रणाली र शासन व्यवस्था",
                "description_en": "Nepal's political system and governance",
                "icon": "🏛️",
                "color": "#DC143C",
                "category": "राजनीति"
            },
            {
                "name": "अर्थतन्त्र",
                "name_en": "Economy",
                "description": "नेपालको आर्थिक अवस्था र व्यापार",
                "description_en": "Nepal's economic condition and trade",
                "icon": "💰",
                "color": "#FFD700",
                "category": "अर्थतन्त्र"
            },
            {
                "name": "धर्म र दर्शन",
                "name_en": "Religion & Philosophy",
                "description": "नेपालका धार्मिक परम्परा र दार्शनिक चिन्तन",
                "description_en": "Religious traditions and philosophical thoughts of Nepal",
                "icon": "🕉️",
                "color": "#FF8C00",
                "category": "धर्म"
            },
            {
                "name": "कला र संगीत",
                "name_en": "Art & Music",
                "description": "नेपाली कला र संगीतका विविध रूपहरू",
                "description_en": "Various forms of Nepali art and music",
                "icon": "🎨",
                "color": "#9370DB",
                "category": "कला"
            },
            {
                "name": "नृत्य र नाटक",
                "name_en": "Dance & Drama",
                "description": "नेपाली नृत्य र नाटकका परम्परागत रूपहरू",
                "description_en": "Traditional forms of Nepali dance and drama",
                "icon": "💃",
                "color": "#FF1493",
                "category": "नृत्य"
            },
            {
                "name": "खानपान",
                "name_en": "Food & Cuisine",
                "description": "नेपाली खानपानका परम्परागत र आधुनिक रूपहरू",
                "description_en": "Traditional and modern forms of Nepali cuisine",
                "icon": "🍛",
                "color": "#FF4500",
                "category": "खानपान"
            },
            {
                "name": "चाडपर्व",
                "name_en": "Festivals",
                "description": "नेपालका विविध चाडपर्वहरू र तिनका महत्व",
                "description_en": "Various festivals of Nepal and their significance",
                "icon": "🎉",
                "color": "#FF69B4",
                "category": "चाडपर्व"
            },
            {
                "name": "वन्यजन्तु र प्रकृति",
                "name_en": "Wildlife & Nature",
                "description": "नेपालका वन्यजन्तु र प्राकृतिक सम्पदा",
                "description_en": "Wildlife and natural resources of Nepal",
                "icon": "🐅",
                "color": "#228B22",
                "category": "प्रकृति"
            },
            {
                "name": "पर्यटन",
                "name_en": "Tourism",
                "description": "नेपालका पर्यटकीय गन्तव्यहरू र सम्भावनाहरू",
                "description_en": "Tourist destinations and possibilities of Nepal",
                "icon": "🗻",
                "color": "#4682B4",
                "category": "पर्यटन"
            },
            {
                "name": "शिक्षा",
                "name_en": "Education",
                "description": "नेपालको शिक्षा प्रणाली र विकास",
                "description_en": "Education system and development of Nepal",
                "icon": "🎓",
                "color": "#800080",
                "category": "शिक्षा"
            },
            {
                "name": "स्वास्थ्य",
                "name_en": "Health",
                "description": "स्वास्थ्य सेवा र चिकित्सा विज्ञान",
                "description_en": "Health services and medical science",
                "icon": "🏥",
                "color": "#DC143C",
                "category": "स्वास्थ्य"
            },
            {
                "name": "कृषि",
                "name_en": "Agriculture",
                "description": "नेपालको कृषि प्रणाली र खाद्य उत्पादन",
                "description_en": "Nepal's agricultural system and food production",
                "icon": "🌾",
                "color": "#9ACD32",
                "category": "कृषि"
            }
        ]
        
        # Add common fields to all themes
        current_time = datetime.now(timezone.utc)
        for theme in themes:
            theme.update({
                "_id": ObjectId(),
                "is_active": True,
                "created_at": current_time,
                "updated_at": current_time
            })
        
        return themes
    
    def generate_content_sets_for_theme(self, theme: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate 2 content sets for a given theme."""
        content_sets = []
        current_time = datetime.now(timezone.utc)
        
        for i in range(2):
            set_title = f"{theme['name']} - भाग {i + 1}"
            set_title_en = f"{theme['name_en']} - Part {i + 1}"
            
            content_set = {
                "_id": ObjectId(),
                "theme_id": theme["_id"],
                "title": set_title,
                "title_en": set_title_en,
                "description": f"{theme['description']} - {i + 1} औं भाग",
                "description_en": f"{theme['description_en']} - Part {i + 1}",
                "difficulty_level": random.choice([1, 2, 3]),  # 1=easy, 2=medium, 3=hard
                "status": "pending",
                "gentype": "primary",
                "task_item_ids": [],  # Will be populated after creating tasks
                "total_items": 5,
                "metadata": {
                    "generated_by": "system",
                    "generation_prompt": f"Generate questions about {theme['name_en']}",
                    "ai_model": "curated_content_generator",
                    "quality_score": random.uniform(0.8, 1.0)
                },
                "created_at": current_time,
                "updated_at": current_time
            }
            content_sets.append(content_set)
        
        return content_sets

    def get_real_questions_for_theme(self, theme_name: str, theme_category: str) -> List[Dict[str, Any]]:
        """Generate real questions based on theme."""
        questions_by_theme = {
            "भूगोल र वातावरण": [
                {
                    "type": "single_choice",
                    "title": "नेपालको सबैभन्दा अग्लो हिमाल",
                    "question": {
                        "text": "नेपालको सबैभन्दा अग्लो हिमाल कुन हो?",
                        "translated_text": "Which is the highest mountain in Nepal?",
                        "options": {
                            "a": "सगरमाथा",
                            "b": "कञ्चनजङ्घा",
                            "c": "अन्नपूर्ण",
                            "d": "मनास्लु"
                        },
                        "answer_hint": "world's highest peak",
                        "metadata": {}
                    },
                    "correct_answer": {"value": "a", "type": "single"}
                },
                {
                    "type": "multiple_choice",
                    "title": "नेपालका मुख्य नदीहरू",
                    "question": {
                        "text": "नेपालका मुख्य नदीहरू कुन कुन हुन्? (एकभन्दा बढी उत्तर छान्नुहोस्)",
                        "translated_text": "Which are the main rivers of Nepal? (Select multiple answers)",
                        "options": {
                            "a": "कोशी",
                            "b": "गण्डकी",
                            "c": "कर्णाली",
                            "d": "गंगा"
                        },
                        "answer_hint": "major river systems",
                        "metadata": {}
                    },
                    "correct_answer": {"value": ["a", "b", "c"], "type": "multiple"}
                },
                {
                    "type": "single_choice",
                    "title": "नेपालको क्षेत्रफल",
                    "question": {
                        "text": "नेपालको कुल क्षेत्रफल कति छ?",
                        "translated_text": "What is the total area of Nepal?",
                        "options": {
                            "a": "१,४७,१८१ वर्ग किमी",
                            "b": "१,५०,००० वर्ग किमी",
                            "c": "१,२०,००० वर्ग किमी",
                            "d": "२,००,००० वर्ग किमी"
                        },
                        "answer_hint": "exact area in square kilometers",
                        "metadata": {}
                    },
                    "correct_answer": {"value": "a", "type": "single"}
                },
                {
                    "type": "single_choice",
                    "title": "नेपालका भौगोलिक क्षेत्र",
                    "question": {
                        "text": "नेपालमा कति वटा मुख्य भौगोलिक क्षेत्र छन्?",
                        "translated_text": "How many main geographical regions are there in Nepal?",
                        "options": {
                            "a": "२",
                            "b": "३",
                            "c": "४",
                            "d": "५"
                        },
                        "answer_hint": "mountain, hill, terai",
                        "metadata": {}
                    },
                    "correct_answer": {"value": "b", "type": "single"}
                },
                {
                    "type": "multiple_choice",
                    "title": "नेपालका राष्ट्रिय निकुञ्जहरू",
                    "question": {
                        "text": "नेपालका प्रसिद्ध राष्ट्रिय निकुञ्जहरू कुन कुन हुन्?",
                        "translated_text": "Which are the famous national parks of Nepal?",
                        "options": {
                            "a": "चितवन राष्ट्रिय निकुञ्ज",
                            "b": "सगरमाथा राष्ट्रिय निकुञ्ज",
                            "c": "बर्दिया राष्ट्रिय निकुञ्ज",
                            "d": "काठमाडौं निकुञ्ज"
                        },
                        "answer_hint": "famous wildlife parks",
                        "metadata": {}
                    },
                    "correct_answer": {"value": ["a", "b", "c"], "type": "multiple"}
                }
            ],
            "नेपाली संस्कृति": [
                {
                    "type": "single_choice",
                    "title": "नेपालको राष्ट्रिय पशु",
                    "question": {
                        "text": "नेपालको राष्ट्रिय पशु कुन हो?",
                        "translated_text": "What is the national animal of Nepal?",
                        "options": {
                            "a": "गाई",
                            "b": "गैंडा",
                            "c": "बाघ",
                            "d": "हात्ती"
                        },
                        "answer_hint": "one-horned animal",
                        "metadata": {}
                    },
                    "correct_answer": {"value": "b", "type": "single"}
                },
                {
                    "type": "single_choice",
                    "title": "नेपालको राष्ट्रिय फूल",
                    "question": {
                        "text": "नेपालको राष्ट्रिय फूल कुन हो?",
                        "translated_text": "What is the national flower of Nepal?",
                        "options": {
                            "a": "गुराँस",
                            "b": "सुनखरी",
                            "c": "गुलाफ",
                            "d": "कमल"
                        },
                        "answer_hint": "rhododendron",
                        "metadata": {}
                    },
                    "correct_answer": {"value": "a", "type": "single"}
                },
                {
                    "type": "multiple_choice",
                    "title": "नेपाली पारम्परिक पोशाक",
                    "question": {
                        "text": "नेपाली पारम्परिक पोशाकहरू कुन कुन हुन्?",
                        "translated_text": "Which are the traditional Nepali dresses?",
                        "options": {
                            "a": "दौरा सुरुवाल",
                            "b": "गुन्यु चोलो",
                            "c": "धाका टोपी",
                            "d": "जिन्स"
                        },
                        "answer_hint": "traditional clothing",
                        "metadata": {}
                    },
                    "correct_answer": {"value": ["a", "b", "c"], "type": "multiple"}
                },
                {
                    "type": "single_choice",
                    "title": "नेपालको राष्ट्रिय चरा",
                    "question": {
                        "text": "नेपालको राष्ट्रिय चरा कुन हो?",
                        "translated_text": "What is the national bird of Nepal?",
                        "options": {
                            "a": "कागा",
                            "b": "डाँफे",
                            "c": "चील",
                            "d": "हाँस"
                        },
                        "answer_hint": "colorful pheasant",
                        "metadata": {}
                    },
                    "correct_answer": {"value": "b", "type": "single"}
                },
                {
                    "type": "single_choice",
                    "title": "नमस्ते को अर्थ",
                    "question": {
                        "text": "नेपालीहरूले भन्ने 'नमस्ते' को अर्थ के हो?",
                        "translated_text": "What does 'Namaste' said by Nepalis mean?",
                        "options": {
                            "a": "अलविदा",
                            "b": "तपाईंलाई सम्मान गर्छु",
                            "c": "धन्यवाद",
                            "d": "माफ गर्नुहोस्"
                        },
                        "answer_hint": "respectful greeting",
                        "metadata": {}
                    },
                    "correct_answer": {"value": "b", "type": "single"}
                }
            ]
        }

        # Default questions if theme not found
        default_questions = [
            {
                "type": "single_choice",
                "title": f"{theme_name} सम्बन्धी प्रश्न १",
                "question": {
                    "text": f"{theme_name} को बारेमा तपाईंलाई के थाहा छ?",
                    "translated_text": f"What do you know about {theme_name}?",
                    "options": {
                        "a": "धेरै",
                        "b": "केही",
                        "c": "थोरै",
                        "d": "केही थाहा छैन"
                    },
                    "answer_hint": "knowledge level",
                    "metadata": {}
                },
                "correct_answer": {"value": "a", "type": "single"}
            },
            {
                "type": "multiple_choice",
                "title": f"{theme_name} सम्बन्धी प्रश्न २",
                "question": {
                    "text": f"{theme_name} मा तपाईंको रुचि कस्तो छ?",
                    "translated_text": f"What is your interest level in {theme_name}?",
                    "options": {
                        "a": "धेरै रुचि छ",
                        "b": "केही रुचि छ",
                        "c": "सिक्न चाहन्छु",
                        "d": "रुचि छैन"
                    },
                    "answer_hint": "interest level",
                    "metadata": {}
                },
                "correct_answer": {"value": ["a", "b", "c"], "type": "multiple"}
            }
        ]

        theme_questions = questions_by_theme.get(theme_name, default_questions)

        # Ensure we have exactly 5 questions by repeating or truncating
        while len(theme_questions) < 5:
            theme_questions.extend(default_questions)

        return theme_questions[:5]  # Return exactly 5 questions

    def create_task_items_for_content_set(self, content_set: Dict[str, Any], theme: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Create 5 task items for a content set."""
        questions = self.get_real_questions_for_theme(theme["name"], theme["category"])
        task_items = []
        current_time = datetime.now(timezone.utc)

        for i, question_data in enumerate(questions):
            task_item = {
                "_id": ObjectId(),
                "task_set_id": content_set["_id"],
                "user_id": USER_ID,
                "session_id": str(uuid.uuid4()),
                "type": question_data["type"],
                "title": question_data["title"],
                "question": question_data["question"],
                "correct_answer": question_data["correct_answer"],
                "user_answer": None,
                "status": "pending",
                "result": None,
                "remark": None,
                "total_score": 10,
                "scored": 0,
                "submitted": False,
                "submitted_at": None,
                "attempts_count": 0,
                "difficulty_level": content_set["difficulty_level"],
                "metadata": {
                    "_v2_optimized": False,
                    "_media_excluded": False,
                    "_priority": "instant",
                    "_media_ready": True,
                    "theme_id": str(theme["_id"]),
                    "theme_name": theme["name"]
                },
                "created_at": current_time,
                "updated_at": current_time,
                "answered_at": None,
                "is_attempted": False,
                "submitted_by": None,
                "test_results": None,
                "test_status": None,
                "verification_notes": None,
                "verification_status": "pending",
                "verified_at": None,
                "verified_by": None
            }
            task_items.append(task_item)

        return task_items

    def create_curated_content_set(self, content_set: Dict[str, Any], task_item_ids: List[str], theme: Dict[str, Any]) -> Dict[str, Any]:
        """Create a curated content set document."""
        current_time = datetime.now(timezone.utc)

        curated_set = {
            "_id": content_set["_id"],
            "user_id": USER_ID,
            "session_id": str(uuid.uuid4()),
            "title": content_set["title"],
            "input_type": "text",
            "tasks": task_item_ids,
            "stories": [],  # No stories for curated content
            "total_tasks": len(task_item_ids),
            "total_stories": 0,
            "text_tasks_ready": len(task_item_ids),
            "media_tasks_pending": 0,
            "attempted_tasks": 0,
            "total_verified": 0,
            "status": "pending",
            "total_score": len(task_item_ids) * 10,  # 10 points per task
            "scored": 0,
            "attempts_count": 0,
            "created_at": current_time,
            "v2_collection_id": str(uuid.uuid4()),
            "optimization_metadata": {
                "total_tasks": len(task_item_ids),
                "media_excluded_count": 0,
                "media_included_count": 0,
                "optimization_applied": False,
                "stories_extracted": 0
            },
            "usage_metadata": {
                "cache_tokens_details": None,
                "cached_content_token_count": None,
                "candidates_token_count": 0,
                "candidates_tokens_details": [],
                "prompt_token_count": 0,
                "prompt_tokens_details": [],
                "thoughts_token_count": None,
                "tool_use_prompt_token_count": None,
                "tool_use_prompt_tokens_details": None,
                "total_token_count": 0,
                "traffic_type": None
            },
            "service_version": "curated_content_generator",
            "priority_processing": {
                "text_tasks_count": len(task_item_ids),
                "media_tasks_count": 0,
                "stories_count": 0,
                "processing_status": "all_text_ready"
            },
            "input_content": {
                "object_name": f"curated_content/{theme['name']}/{content_set['title']}",
                "bucket_name": "nepali.curated",
                "object_path": f"curated_content/{theme['name']}/{content_set['title']}",
                "file_name": f"{content_set['title']}.txt",
                "content_type": "text/plain",
                "size_bytes": 0,
                "folder": "curated_content",
                "session_id": str(uuid.uuid4()),
                "created_at": current_time.isoformat(),
                "file_extension": ".txt"
            },
            "updated_at": current_time,
            "completed_at": None,
            "remark": f"Curated content set for theme: {theme['name']}",
            "submitted_at": None,
            "theme_id": theme["_id"]  # Important: Link to theme
        }

        return curated_set

    async def generate_all_content(self):
        """Generate all themes, content sets, and task items."""
        print("🚀 Starting curated content generation...")

        # Generate themes
        print("📝 Generating themes...")
        themes = self.generate_themes()
        print(f"✅ Generated {len(themes)} themes")

        # Insert themes into database
        if themes:
            await self.db.themes.insert_many(themes)
            print("✅ Themes inserted into database")

        all_content_sets = []
        all_task_items = []

        # Generate content sets and task items for each theme
        for theme in themes:
            print(f"🎯 Processing theme: {theme['name']}")

            # Generate 2 content sets per theme
            content_sets = self.generate_content_sets_for_theme(theme)

            for content_set in content_sets:
                print(f"  📚 Creating content set: {content_set['title']}")

                # Create task items for this content set
                task_items = self.create_task_items_for_content_set(content_set, theme)
                task_item_ids = [str(item["_id"]) for item in task_items]

                # Update content set with task item IDs
                content_set["task_item_ids"] = task_item_ids

                # Create curated content set document
                curated_set = self.create_curated_content_set(content_set, task_item_ids, theme)

                all_content_sets.append(curated_set)
                all_task_items.extend(task_items)

                print(f"    ✅ Created {len(task_items)} task items")

        # Insert content sets into database
        if all_content_sets:
            await self.db.curated_content_set.insert_many(all_content_sets)
            print(f"✅ Inserted {len(all_content_sets)} content sets")

        # Insert task items into database
        if all_task_items:
            await self.db.task_items.insert_many(all_task_items)
            print(f"✅ Inserted {len(all_task_items)} task items")

        # Also insert into curated_content_items collection
        if all_task_items:
            await self.db.curated_content_items.insert_many(all_task_items)
            print(f"✅ Inserted {len(all_task_items)} curated content items")

        print("\n🎉 Content generation completed successfully!")
        print(f"📊 Summary:")
        print(f"   - Themes: {len(themes)}")
        print(f"   - Content Sets: {len(all_content_sets)}")
        print(f"   - Task Items: {len(all_task_items)}")

        return {
            "themes": len(themes),
            "content_sets": len(all_content_sets),
            "task_items": len(all_task_items)
        }


async def main():
    """Main function to run the content generation."""
    generator = CuratedContentGenerator(DB_URL, DB_NAME)

    try:
        # Clear existing data (optional - comment out if you want to keep existing data)
        print("🧹 Clearing existing curated content...")
        await generator.db.themes.delete_many({})
        await generator.db.curated_content_set.delete_many({})
        await generator.db.curated_content_items.delete_many({})
        print("✅ Existing data cleared")

        # Generate new content
        result = await generator.generate_all_content()

        print(f"\n✨ Generation completed: {result}")

    except Exception as e:
        print(f"❌ Error during generation: {e}")
        raise
    finally:
        await generator.close()


if __name__ == "__main__":
    asyncio.run(main())
