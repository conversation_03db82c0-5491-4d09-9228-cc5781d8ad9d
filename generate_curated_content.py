#!/usr/bin/env python3
"""
Script to generate curated content with themes, content sets, and task items.

This script generates:
- 15-20 themes (like "भूगोल र वातावरण")
- 2 content sets per theme
- 5 questions per set (mix of single choice and multiple choice)

Database: mongodb://localhost:27017/
"""

import asyncio
import random
from datetime import datetime, timezone
from bson import ObjectId
from motor.motor_asyncio import AsyncIOMotorClient
from typing import List, Dict, Any
import uuid

# Database configuration
DB_URL = "mongodb://localhost:27017/"
DB_NAME = "nepali_app_tenant_1"  # Default tenant database

class CuratedContentGenerator:
    def __init__(self, db_url: str, db_name: str):
        self.client = AsyncIOMotorClient(db_url)
        self.db = self.client[db_name]
        
    async def close(self):
        """Close database connection."""
        self.client.close()
    
    def generate_themes(self) -> List[Dict[str, Any]]:
        """Generate 15-20 themes with Nepali content."""
        themes = [
            {
                "name": "भूगोल र वातावरण",
                "name_en": "Geography & Environment",
                "description": "नेपालको भौगोलिक संरचना र वातावरणीय विविधता",
                "description_en": "Nepal's geographical structure and environmental diversity",
                "icon": "🏔️",
                "color": "#2E8B57",
                "category": "भूगोल"
            },
            {
                "name": "नेपाली संस्कृति",
                "name_en": "Nepali Culture",
                "description": "नेपाली संस्कृति र परम्पराका बारेमा",
                "description_en": "About Nepali culture and traditions",
                "icon": "🏛️",
                "color": "#FF6B6B",
                "category": "संस्कृति"
            },
            {
                "name": "इतिहास र विरासत",
                "name_en": "History & Heritage",
                "description": "नेपालको ऐतिहासिक घटनाहरू र सांस्कृतिक विरासत",
                "description_en": "Nepal's historical events and cultural heritage",
                "icon": "📜",
                "color": "#8B4513",
                "category": "इतिहास"
            },
            {
                "name": "भाषा र साहित्य",
                "name_en": "Language & Literature",
                "description": "नेपाली भाषा र साहित्यका विविध पक्षहरू",
                "description_en": "Various aspects of Nepali language and literature",
                "icon": "📚",
                "color": "#4169E1",
                "category": "भाषा"
            },
            {
                "name": "विज्ञान र प्रविधि",
                "name_en": "Science & Technology",
                "description": "आधुनिक विज्ञान र प्रविधिका बारेमा",
                "description_en": "About modern science and technology",
                "icon": "🔬",
                "color": "#00CED1",
                "category": "विज्ञान"
            },
            {
                "name": "खेलकुद",
                "name_en": "Sports",
                "description": "नेपाली र अन्तर्राष्ट्रिय खेलकुदका बारेमा",
                "description_en": "About Nepali and international sports",
                "icon": "⚽",
                "color": "#32CD32",
                "category": "खेलकुद"
            },
            {
                "name": "राजनीति र शासन",
                "name_en": "Politics & Governance",
                "description": "नेपालको राजनीतिक प्रणाली र शासन व्यवस्था",
                "description_en": "Nepal's political system and governance",
                "icon": "🏛️",
                "color": "#DC143C",
                "category": "राजनीति"
            },
            {
                "name": "अर्थतन्त्र",
                "name_en": "Economy",
                "description": "नेपालको आर्थिक अवस्था र व्यापार",
                "description_en": "Nepal's economic condition and trade",
                "icon": "💰",
                "color": "#FFD700",
                "category": "अर्थतन्त्र"
            },
            {
                "name": "धर्म र दर्शन",
                "name_en": "Religion & Philosophy",
                "description": "नेपालका धार्मिक परम्परा र दार्शनिक चिन्तन",
                "description_en": "Religious traditions and philosophical thoughts of Nepal",
                "icon": "🕉️",
                "color": "#FF8C00",
                "category": "धर्म"
            },
            {
                "name": "कला र संगीत",
                "name_en": "Art & Music",
                "description": "नेपाली कला र संगीतका विविध रूपहरू",
                "description_en": "Various forms of Nepali art and music",
                "icon": "🎨",
                "color": "#9370DB",
                "category": "कला"
            },
            {
                "name": "नृत्य र नाटक",
                "name_en": "Dance & Drama",
                "description": "नेपाली नृत्य र नाटकका परम्परागत रूपहरू",
                "description_en": "Traditional forms of Nepali dance and drama",
                "icon": "💃",
                "color": "#FF1493",
                "category": "नृत्य"
            },
            {
                "name": "खानपान",
                "name_en": "Food & Cuisine",
                "description": "नेपाली खानपानका परम्परागत र आधुनिक रूपहरू",
                "description_en": "Traditional and modern forms of Nepali cuisine",
                "icon": "🍛",
                "color": "#FF4500",
                "category": "खानपान"
            },
            {
                "name": "चाडपर्व",
                "name_en": "Festivals",
                "description": "नेपालका विविध चाडपर्वहरू र तिनका महत्व",
                "description_en": "Various festivals of Nepal and their significance",
                "icon": "🎉",
                "color": "#FF69B4",
                "category": "चाडपर्व"
            },
            {
                "name": "वन्यजन्तु र प्रकृति",
                "name_en": "Wildlife & Nature",
                "description": "नेपालका वन्यजन्तु र प्राकृतिक सम्पदा",
                "description_en": "Wildlife and natural resources of Nepal",
                "icon": "🐅",
                "color": "#228B22",
                "category": "प्रकृति"
            },
            {
                "name": "पर्यटन",
                "name_en": "Tourism",
                "description": "नेपालका पर्यटकीय गन्तव्यहरू र सम्भावनाहरू",
                "description_en": "Tourist destinations and possibilities of Nepal",
                "icon": "🗻",
                "color": "#4682B4",
                "category": "पर्यटन"
            },
            {
                "name": "शिक्षा",
                "name_en": "Education",
                "description": "नेपालको शिक्षा प्रणाली र विकास",
                "description_en": "Education system and development of Nepal",
                "icon": "🎓",
                "color": "#800080",
                "category": "शिक्षा"
            },
            {
                "name": "स्वास्थ्य",
                "name_en": "Health",
                "description": "स्वास्थ्य सेवा र चिकित्सा विज्ञान",
                "description_en": "Health services and medical science",
                "icon": "🏥",
                "color": "#DC143C",
                "category": "स्वास्थ्य"
            },
            {
                "name": "कृषि",
                "name_en": "Agriculture",
                "description": "नेपालको कृषि प्रणाली र खाद्य उत्पादन",
                "description_en": "Nepal's agricultural system and food production",
                "icon": "🌾",
                "color": "#9ACD32",
                "category": "कृषि"
            }
        ]
        
        # Add common fields to all themes
        current_time = datetime.now(timezone.utc)
        for theme in themes:
            theme.update({
                "_id": ObjectId(),
                "is_active": True,
                "created_at": current_time,
                "updated_at": current_time
            })
        
        return themes
    
    def generate_content_sets_for_theme(self, theme: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate 2 content sets for a given theme."""
        content_sets = []
        current_time = datetime.now(timezone.utc)
        
        for i in range(2):
            set_title = f"{theme['name']} - भाग {i + 1}"
            set_title_en = f"{theme['name_en']} - Part {i + 1}"
            
            content_set = {
                "_id": ObjectId(),
                "theme_id": theme["_id"],
                "title": set_title,
                "title_en": set_title_en,
                "description": f"{theme['description']} - {i + 1} औं भाग",
                "description_en": f"{theme['description_en']} - Part {i + 1}",
                "difficulty_level": random.choice([1, 2, 3]),  # 1=easy, 2=medium, 3=hard
                "status": "pending",
                "gentype": "primary",
                "task_item_ids": [],  # Will be populated after creating tasks
                "total_items": 5,
                "metadata": {
                    "generated_by": "system",
                    "generation_prompt": f"Generate questions about {theme['name_en']}",
                    "ai_model": "curated_content_generator",
                    "quality_score": random.uniform(0.8, 1.0)
                },
                "created_at": current_time,
                "updated_at": current_time
            }
            content_sets.append(content_set)
        
        return content_sets
