#!/bin/bash

# Test Editor Prompts Endpoint in Management Service
# This script tests the moved get_prompts endpoint

set -e

# Configuration
API_PORT=${API_PORT:-8204}
BASE_URL="http://localhost:${API_PORT}"
MANAGEMENT_URL="${BASE_URL}/v1/management"
EDITOR_URL="${MANAGEMENT_URL}/editor"

# Test JWT token (you'll need to replace this with a valid token)
JWT_TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8"

echo "🧪 Testing Editor Prompts Endpoint in Management Service..."
echo "📍 Base URL: ${BASE_URL}"
echo "📍 Management URL: ${MANAGEMENT_URL}"
echo "📍 Editor URL: ${EDITOR_URL}"

# Test 1: Health check for management service
echo ""
echo "1️⃣ Testing management service health..."
curl -f "${MANAGEMENT_URL}/health" || echo "❌ Management health check failed"

# Test 2: Get prompts with default pagination
echo ""
echo "2️⃣ Testing get prompts endpoint (default pagination)..."
curl -X POST "${EDITOR_URL}/get_prompts" \
  -H "accept: application/json" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${JWT_TOKEN}" || echo "❌ Get prompts failed"

# Test 3: Get prompts with custom pagination
echo ""
echo "3️⃣ Testing get prompts endpoint (custom pagination)..."
curl -X POST "${EDITOR_URL}/get_prompts?page=1&limit=5&sort_order=asc" \
  -H "accept: application/json" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${JWT_TOKEN}" || echo "❌ Get prompts with pagination failed"

# Test 4: Get prompts with desc order
echo ""
echo "4️⃣ Testing get prompts endpoint (desc order)..."
curl -X POST "${EDITOR_URL}/get_prompts?page=1&limit=3&sort_order=desc" \
  -H "accept: application/json" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${JWT_TOKEN}" || echo "❌ Get prompts desc order failed"

echo ""
echo "✅ Editor prompts endpoint tests completed!"
echo "📍 New endpoint location: ${EDITOR_URL}/get_prompts"
