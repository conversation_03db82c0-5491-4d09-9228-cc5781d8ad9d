# To run this code you need to install the following dependencies:
# pip install google-genai

import mimetypes
import os
import struct
from google import genai
from google.genai import types
from typing import Union
from app.shared.models.user import UserTenantDB
from app.shared.utils.logger import setup_new_logging
import hashlib
loggers = setup_new_logging(__name__)


def convert_to_wav(audio_data: bytes, mime_type: str) -> bytes:
    """Generates a WAV file header for the given audio data and parameters.

    Args:
        audio_data: The raw audio data as a bytes object.
        mime_type: Mime type of the audio data.

    Returns:
        A bytes object representing the WAV file header.
    """
    parameters = parse_audio_mime_type(mime_type)
    bits_per_sample = parameters["bits_per_sample"]
    sample_rate = parameters["rate"]
    num_channels = 1
    data_size = len(audio_data)
    bytes_per_sample = bits_per_sample // 8
    block_align = num_channels * bytes_per_sample
    byte_rate = sample_rate * block_align
    chunk_size = 36 + data_size  # 36 bytes for header fields before data chunk size

    # http://soundfile.sapp.org/doc/WaveFormat/

    header = struct.pack(
        "<4sI4s4sIHHIIHH4sI",
        b"RIFF",          # ChunkID
        chunk_size,       # ChunkSize (total file size - 8 bytes)
        b"WAVE",          # Format
        b"fmt ",          # Subchunk1ID
        16,               # Subchunk1Size (16 for PCM)
        1,                # AudioFormat (1 for PCM)
        num_channels,     # NumChannels
        sample_rate,      # SampleRate
        byte_rate,        # ByteRate
        block_align,      # BlockAlign
        bits_per_sample,  # BitsPerSample
        b"data",          # Subchunk2ID
        data_size         # Subchunk2Size (size of audio data)
    )
    return header + audio_data


def parse_audio_mime_type(mime_type: str) -> dict[str, int]:
    """Parses bits per sample and rate from an audio MIME type string.

    Assumes bits per sample is encoded like "L16" and rate as "rate=xxxxx".

    Args:
        mime_type: The audio MIME type string (e.g., "audio/L16;rate=24000").

    Returns:
        A dictionary with "bits_per_sample" and "rate" keys. Values will be
        integers if found, otherwise None.
    """
    bits_per_sample = 16
    rate = 24000

    # Extract rate from parameters
    parts = mime_type.split(";")
    for param in parts: # Skip the main type part
        param = param.strip()
        if param.lower().startswith("rate="):
            try:
                rate_str = param.split("=", 1)[1]
                rate = int(rate_str)
            except (ValueError, IndexError):
                # Handle cases like "rate=" with no value or non-integer value
                pass # Keep rate as default
        elif param.startswith("audio/L"):
            try:
                bits_per_sample = int(param.split("L", 1)[1])
            except (ValueError, IndexError):
                pass # Keep bits_per_sample as default if conversion fails

    return {"bits_per_sample": bits_per_sample, "rate": rate}


async def generate(current_user: UserTenantDB, keyword: Union[str, list[str]] ,type:str="audio_prompt"):
    if isinstance(keyword,list):
        keyword = ", ".join(keyword)
    loggers.info(f"Generating audio for keyword: {keyword}")

    # Try to get prompt from database, fallback to hardcoded
    try:
        prompt_data = await current_user.async_db.prompts.find_one({"name": type})
        if not prompt_data:
            loggers.warning("No prompt found in database, using default prompt.")
            prompt_data = {"prompt": "Generate audio for the keyword: {keyword}"}
    except Exception as e:
        loggers.error(f"Error fetching prompt from database: {e}")
        prompt_data = {"prompt": "Generate audio for the keyword: {keyword}"}
    loggers.debug(f"Generating audio for: {keyword}")
    prompt = prompt_data.get("prompt", "").format(keyword=keyword)
    client = genai.Client(api_key=os.environ.get("GEMINI_API_KEY"))

    model = "gemini-2.5-flash-preview-tts"
    contents = [
        types.Content(
            role="user",
            parts=[types.Part.from_text(text=prompt)],
        ),
    ]
    generate_content_config = types.GenerateContentConfig(
        temperature=1,
        response_modalities=["audio"],
        speech_config=types.SpeechConfig(
            voice_config=types.VoiceConfig(
                prebuilt_voice_config=types.PrebuiltVoiceConfig(voice_name="Sulafat")
            )
        ),
    )

    file_text = ""
    file_bytes = b""
    usage_metadata = {}

    try:
        for chunk in client.models.generate_content_stream(
            model=model,
            contents=contents,
            config=generate_content_config,
        ):
            if (
                chunk.candidates is None
                or chunk.candidates[0].content is None
                or chunk.candidates[0].content.parts is None
            ):
                continue

            # Process audio data
            if chunk.candidates[0].content.parts[0].inline_data and chunk.candidates[0].content.parts[0].inline_data.data:
                inline_data = chunk.candidates[0].content.parts[0].inline_data
                data_buffer = inline_data.data

                # Convert to WAV if needed
                file_extension = mimetypes.guess_extension(inline_data.mime_type)
                if file_extension is None:
                    file_extension = ".wav"
                    data_buffer = convert_to_wav(inline_data.data, inline_data.mime_type)

                file_bytes += data_buffer
            else:
                # Collect any text content
                if hasattr(chunk, 'text') and chunk.text:
                    file_text += chunk.text

            # Capture usage metadata from chunks
            if chunk.usage_metadata:
                usage_metadata = chunk.usage_metadata

    except Exception as api_error:
        loggers.error(f"Audio generation failed for '{keyword}': {api_error}")
        return None, None, {}

    # Check if we got audio data
    if not file_bytes:
        loggers.error(f"No audio data received for: {keyword}")
        return None, None, usage_metadata

    # Determine file extension and content type
    if file_bytes.startswith(b'RIFF'):
        file_extension = ".wav"
        content_type = "audio/wav"
    else:
        file_extension = ".mp3"
        content_type = "audio/mpeg"

    # Save to MinIO
    from app.shared.async_minio_client import create_async_minio_client

    async_minio_client = create_async_minio_client(current_user.minio)
    file_info = await async_minio_client.save_file_async(
        data=file_bytes,
        user_id=current_user.user.id,
        content_type=content_type,
        folder="audiogen",
        file_extension=file_extension,
        # custom_filename=hashlib.sha256(f"{keyword}_audio{file_extension}").hexdigest()[:10] 
        custom_filename=hashlib.sha256(f"{keyword}_audio{file_extension}".encode()).hexdigest()[:10] + file_extension
    )

    loggers.info(f"Audio generated and saved: {keyword}")
    return file_text, file_info, usage_metadata





if __name__ == "__main__":
    # For testing without user context, create a simple test
    print("Audio generation function updated to work with user context and Minio")
    print("Use this function in the task generation system with proper UserTenantDB context")