from datetime import datetime, timezone
from bson import ObjectId
from pymongo.errors import PyMongoError
from fastapi import HTTPException, BackgroundTasks
from app.shared.models.user import UserTenantDB
from app.shared.db_enums import CollectionName
from app.shared.utils.logger import setup_new_logging
from app.shared.models.curated_content import CuratedContentRequest
from app.v2.api.editor_service.generators import (
    generate_content,
    generate_audio,
    generate_tasks,
    generate_image,
    generate_audio_task,
)
from app.v2.api.editor_service.creator.split_and_manage import (
    split_and_manage,
    save_task_set_to_db,
    save_task_items_to_db,
    save_story_items_to_db,
    generate_media_for_tasks_and_stories
)

loggers = setup_new_logging(__name__)


async def save_to_database(
    request: CuratedContentRequest,
    current_user: UserTenantDB,
    background_tasks: BackgroundTasks
) -> dict:
    """
    Save curated content request to database with pending status and start background processing.
    Following v2 audio process pattern: insert immediately, then process in background.
    """
    try:
        # Generate task_set_id that will be used throughout the process
        task_id = ObjectId()
        
        loggers.info(f"Saving content {request.content} for user {current_user.user.id}")
        prompt_id=ObjectId()
        # Create initial document in editor_prompts collection with pending status
        editor_prompt_doc = {
            "_id": prompt_id,
            "curated_content_set_id": task_id,  # This will be the same ID used for curated_content_set
            "user_id": ObjectId(current_user.user.id),
            "prompt": request.content,
            "status": "pending",  # Start with pending status
            "title": "Untitled Task Set",
            "content_type": "curated_content",
            "created_at": datetime.now(timezone.utc),
            "updated_at": datetime.now(timezone.utc)
        }

        # Insert immediately to database (following v2 pattern)
        await current_user.async_db[CollectionName.editor_prompts].insert_one(editor_prompt_doc)
        loggers.info(f"✅ Content stored with pending status: {task_id}")

        return {
            "task_id": str(task_id),
            "status": "pending_generation",
            "message": "Content generation started in background"
        }

    except PyMongoError as e:
        loggers.error(f"Database error saving content: {e}")
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")
    except Exception as e:
        loggers.error(f"Unexpected error saving content: {e}")
        raise HTTPException(status_code=500, detail=f"Unexpected error: {str(e)}")

    finally:
        loggers.info(f"🔄 Starting background processing for task_id: {task_id}")

        # Start background task to process the content
        background_tasks.add_task(_process_curated_content_background,
            current_user, request, task_id,prompt_id
        ) 
        # import asyncio
        # asyncio.create_task(_process_curated_content_background(
        #     current_user, request, task_id
        # ))

async def _process_curated_content_background(
    current_user: UserTenantDB,
    request: CuratedContentRequest,
    task_id: ObjectId  ,# This is the same task_id stored in editor_prompts as task_set_id
    prompt_id: ObjectId
):
    """Background processing of curated content after initial storage."""
    try:
        # Update status to 'generation'
        await current_user.async_db[CollectionName.editor_prompts].update_one(
            {"task_set_id": prompt_id},  # Find by task_set_id
            {"$set": {"status": "processing", "updated_at": datetime.now(timezone.utc)}}
        )
        loggers.info(f"🔄 Started generation for task_id: {task_id}")

        # Use the same task_id as task_set_id (no new ObjectId!)
        task_set_id = task_id

        # step1 generate script
        generated_script, _usage_metadata = await generate_content(request.content)
        loggers.info(f"Generated script: {generated_script}")

        # step2 generate audio
        generated_audio, _audio_usage_metadata = await generate_audio(generated_script)
        loggers.info(f"Generated audio bytes length: {len(generated_audio) if generated_audio else 0}")

        # Save generated audio to MinIO (following v2 pattern)
        audio_storage_info = None
        if generated_audio and current_user.minio:
            try:
                from app.shared.async_minio_client import create_async_minio_client
                import hashlib

                async_minio_client = create_async_minio_client(current_user.minio)

                # Create a unique filename for the curated content audio
                content_hash = hashlib.sha256(request.content.encode()).hexdigest()[:10]
                custom_filename = f"curated_{content_hash}_audio.wav"

                audio_storage_info = await async_minio_client.save_file_async(
                    data=generated_audio,
                    user_id=current_user.user.id,
                    content_type="audio/wav",
                    folder="curated_audio",
                    file_extension=".wav",
                    custom_filename=custom_filename,
                )
                loggers.info(f"📁 Curated audio stored in MinIO: {audio_storage_info.get('object_path')}")
            except Exception as e:
                loggers.error(f"❌ Error storing curated audio in MinIO: {e}")
                audio_storage_info = {}

        # step3 generate tasks
        generated_tasks, _tasks_usage_metadata = await generate_tasks(generated_audio)
        loggers.info(f"Generated tasks: {generated_tasks}")
        if isinstance(generated_tasks,list):
            generated_tasks=generated_tasks[0]
        
        await current_user.async_db[CollectionName.editor_prompts].update_one(
            {"_id": prompt_id},
            {"$set": {"title": generated_tasks.get("title", "Untitled Task Set"), "status": "generating", "updated_at": datetime.now(timezone.utc)}}
        )
        loggers.info(f"Updated editor_prompt with generated_tasks: {prompt_id}")

        # step4 split properly make three coll wise datast task_sets, task_items, and story
        task_set, task_items, story_items = await split_and_manage(generated_tasks)
        loggers.info(f"Split complete: task_set with {len(task_items)} task items, {len(story_items)} story items")

        # step5 save to database following v2 pattern: insert first, then generate media

        # Generate ObjectIds for task set and items

        # First: Save task items and story items to database WITHOUT media
        task_item_ids=await save_task_items_to_db(current_user, task_items, task_set_id)
        story_item_ids=await save_story_items_to_db(current_user, story_items, task_set_id)

        # Update task_set with the actual task and story IDs
        task_set["tasks"] = [str(task_id) for task_id in task_item_ids]
        task_set["gentype"]="curated"
        task_set["stories"] = [str(story_id) for story_id in story_item_ids]
        task_set["total_score"] = sum(item["total_score"] for item in task_items)
        task_set["usage_metadata"] = _tasks_usage_metadata

        # Second: Save task set with references to task and story IDs
        await save_task_set_to_db(
            current_user, task_set, task_set_id,
            generated_script, generated_audio, request.content, audio_storage_info
        )

        # Third: Generate media for task items and story items
        await generate_media_for_tasks_and_stories(
            current_user, task_item_ids, story_item_ids,
            generate_image, generate_audio_task
        )

        # Update status to 'completed'
        await current_user.async_db[CollectionName.editor_prompts].update_one(
            {"task_set_id": prompt_id},  # Find by task_set_id
            {"$set": {
                "status": "completed",
                "task_count": len(task_item_ids),
                "story_count": len(story_item_ids),
                "updated_at": datetime.now(timezone.utc)
            }}
        )

        loggers.info(f"✅ Completed processing for task_id: {task_id}: task_set_id={task_set_id}")

    except Exception as e:
        import traceback
        traceback.print_exc()
        loggers.error(f"❌ Background processing failed for task_id: {task_id}: {e}")
        # Update status to 'failed'
        try:
            await current_user.async_db[CollectionName.editor_prompts].update_one(
                {"task_set_id": prompt_id},  # Find by task_set_id
                {"$set": {
                    "status": "failed",
                    "error": str(e),
                    "updated_at": datetime.now(timezone.utc)
                }}
            )
        except Exception as update_error:
            loggers.error(f"Failed to update error status: {update_error}")



