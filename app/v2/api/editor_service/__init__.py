
"""
Editor Service - Curated Content Generation Service

This service handles curated content generation including:
- Generate curated content from story prompts
- Get prompts for users
- Background processing for media generation
- Database operations for editor prompts

Key Features:
- FastAPI application for editor functionality
- Background task processing for content generation
- MongoDB integration for storing prompts and content
- Authentication and user management integration

Architecture:
- FastAPI application with editor routes
- Background task processing for media generation
- Uses existing MongoDB collections (editor_prompts, task_sets, story_steps)
- Integration with shared authentication and database systems
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from datetime import datetime

from app.shared.utils.logger import setup_new_logging

# Import routes
from app.v2.api.editor_service.routes.editor import router as editor_router

# Configure logging
logger = setup_new_logging(__name__)


# Create FastAPI instance
app = FastAPI(
    title="Nepali App - Editor Service",
    description="Curated content generation service for creating tasks and stories from prompts",
    version="1.0.0",
    docs_url=None,
    redoc_url=None,
    swagger_ui_oauth2_redirect_url="/v2/editor/docs/oauth2-redirect",
    openapi_url="/openapi.json",
    servers=[
        {
            "url": "/v2/editor",
            "description": "Editor Service API"
        }
    ]
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(editor_router, tags=["Curated Editor"])


# Custom OpenAPI schema generation to ensure correct server URLs
def custom_openapi():
    if app.openapi_schema:
        return app.openapi_schema

    from fastapi.openapi.utils import get_openapi
    openapi_schema = get_openapi(
        title=app.title,
        version=app.version,
        description=app.description,
        routes=app.routes,
    )

    # Ensure the server URL is correctly set
    openapi_schema["servers"] = [
        {
            "url": "/v2/editor",
            "description": "Editor Service API"
        }
    ]

    app.openapi_schema = openapi_schema
    return app.openapi_schema

app.openapi = custom_openapi


# Root endpoint
@app.get("/")
async def root():
    """
    Root endpoint for Editor Service.

    Returns basic service information and available endpoints.
    """
    return {
        "service": "Nepali App - Editor Service",
        "version": "1.0.0",
        "description": "Curated content generation service for creating tasks and stories from prompts",
        "timestamp": datetime.now().isoformat(),
        "features": [
            "Curated content generation from story prompts",
            "Background media generation for tasks and stories",
            "User prompt management and retrieval",
            "Integration with task_sets and story_steps collections",
            "Authentication and user management integration"
        ],
        "endpoints": {
            "generate": "/v2/editor/generate - Generate curated content from story prompt",
            "get_prompts": "/v2/editor/get_prompts - Retrieve user prompts",
            "health": "/v2/editor/health - Service health check",
            "docs": "/v2/editor/docs - API documentation"
        },
        "workflow": {
            "1": "POST /v2/editor/generate with story prompt",
            "2": "Service generates tasks and stories",
            "3": "Content saved to database immediately",
            "4": "Background processing for media generation",
            "5": "Returns task_set_id and story_set_id"
        },
        "features_detail": {
            "immediate_response": "Returns immediately after database save",
            "background_processing": "Media generation happens in background",
            "collection_based": "Uses existing task_sets and story_steps collections"
        }
    }


# Custom docs endpoints
@app.get("/docs", include_in_schema=False)
async def custom_swagger_ui_html():
    """Custom Swagger UI for Editor Service."""
    from fastapi.openapi.docs import get_swagger_ui_html
    return get_swagger_ui_html(
        openapi_url="/v2/editor/openapi.json",
        title="Editor Service API",
        swagger_favicon_url="/static/favicon.ico",
        oauth2_redirect_url="/v2/editor/docs/oauth2-redirect",
    )


@app.get("/redoc", include_in_schema=False)
async def redoc_html():
    """Custom ReDoc for Editor Service."""
    from fastapi.openapi.docs import get_redoc_html
    return get_redoc_html(
        openapi_url="/v2/editor/openapi.json",
        title="Editor Service API",
    )


@app.get("/docs/oauth2-redirect", include_in_schema=False)
async def swagger_ui_redirect():
    """OAuth2 redirect endpoint for Swagger UI authentication."""
    from fastapi.openapi.docs import get_swagger_ui_oauth2_redirect_html
    return get_swagger_ui_oauth2_redirect_html()


@app.get("/openapi.json", include_in_schema=False)
async def get_openapi_schema():
    """Get OpenAPI schema for Editor Service."""
    return app.openapi()


@app.get("/health")
async def health_check():
    """
    Health check endpoint for Editor Service.

    Returns the health status of the editor service components.
    """
    return {
        "service": "Nepali App - Editor Service",
        "version": "1.0.0",
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "components": {
            "editor_routes": {
                "status": "healthy",
                "message": "Editor routes are operational"
            },
            "background_processing": {
                "status": "healthy",
                "message": "Background task processing available"
            }
        },
        "features": {
            "curated_content_generation": True,
            "prompt_management": True,
            "background_media_generation": True,
            "database_integration": True
        }
    }



@app.get("/health/detailed")
async def detailed_health():
    """Detailed health check for Editor Service."""
    try:
        health_data = {
            "service": "editor_service",
            "version": "1.0.0",
            "timestamp": datetime.now().isoformat(),
            "status": "healthy",
            "components": {
                "editor_routes": {"status": "healthy"},
                "background_processing": {"status": "healthy"},
                "database_integration": {"status": "healthy"}
            }
        }

        return health_data

    except Exception as e:
        return {
            "status": "unhealthy",
            "service": "editor_service",
            "version": "1.0.0",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }


@app.get("/status")
async def service_status():
    """
    Get detailed service status and statistics for Editor Service.

    Returns information about service capabilities and current status.
    """
    return {
        "service": "Editor Service",
        "version": "1.0.0",
        "status": "running",
        "timestamp": datetime.now().isoformat(),
        "features": {
            "curated_content_generation": True,
            "prompt_management": True,
            "background_media_generation": True,
            "database_integration": True,
            "authentication_integration": True
        },
        "capabilities": {
            "story_prompt_processing": "Generate tasks and stories from prompts",
            "background_processing": "Media generation happens in background",
            "immediate_response": "Returns immediately after database save",
            "collection_based_storage": "Uses existing task_sets and story_steps"
        },
        "endpoints": {
            "generate": "/generate",
            "get_prompts": "/get_prompts",
            "health": "/health",
            "status": "/status",
            "docs": "/docs"
        }
    }


# Global exception handler
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler for Editor Service."""
    logger.error(f"Unhandled exception in Editor Service: {str(exc)}")
    logger.error(f"Request URL: {request.url}")
    return JSONResponse(
        status_code=500,
        content={
            "service": "Editor Service",
            "error": "Internal server error",
            "message": "An unexpected error occurred in the Editor Service",
            "timestamp": datetime.now().isoformat()
        }
    )

