"""
Editor prompts routes for the Management Service.

This module handles editor prompt operations including:
- Getting prompts with pagination using aggregation
"""

from fastapi import APIRouter, Depends, HTTPException, Query
from typing import Dict, Any
from pymongo.errors import PyMongoError

from app.shared.models.user import UserTenantDB
from app.shared.security import get_tenant_info
from app.shared.utils.logger import setup_new_logging
from app.shared.db_enums import CollectionName
from app.shared.utils.mongo_helper import serialize_mongo_doc
from typing_extensions import Literal
from bson import ObjectId

# Configure logging
loggers = setup_new_logging(__name__)

# Create router
router = APIRouter()


@router.post("/get_prompts", response_model=Dict[str, Any])
async def get_prompts(
    page: int = Query(1, ge=1, description="Page number (starts from 1)"),
    limit: int = Query(10, ge=1, le=100, description="Number of items per page (max 100)"),
    sort_order:Literal["asc", "desc"] = Query("desc", description="Sort order: asc or desc"),
    current_user: UserTenantDB = Depends(get_tenant_info),
):
    """
    Retrieve prompts for the current user with pagination using aggregation.
    
    Args:
        page (int): Page number (starts from 1).
        limit (int): Number of items per page (max 100).
        sort_order (str): Sort order: "asc" for ascending, "desc" for descending.
        current_user (UserTenantDB): The current user information.
    
    Returns:
        Dict[str, Any]: A dictionary containing paginated prompts with metadata.
    """
    try:
        # Calculate skip value for pagination
        skip = (page - 1) * limit
        
        # Build match stage for aggregation pipeline
        # match_stage = {"user_id": ObjectId(current_user.user.id)}
        
        # Determine sort direction (PyMongo style)
        sort_direction = -1 
        if sort_order == "asc":
            sort_direction = 1
        
        # Build the aggregation pipeline
        pipeline = [
            # Match stage - filter by user_id
            # {"$match": match_stage},
            
            # Sort stage - sort by created_at
            {"$sort": {"created_at": sort_direction}},
            
            # Facet stage for pagination
            {"$facet": {
                "metadata": [
                    {"$count": "total"}
                ],
                "data": [
                    {"$skip": skip},
                    {"$limit": limit}
                ]
            }}
        ]
        
        # Execute the aggregation pipeline
        cursor = current_user.async_db[CollectionName.editor_prompts].aggregate(pipeline)
        result_list = await (await cursor).to_list(length=1)
        
        # Extract data and metadata
        result = result_list[0] if result_list else {"metadata": [], "data": []}
        
        # Get total count
        total = result["metadata"][0]["total"] if result["metadata"] else 0
        
        # Calculate total pages
        total_pages = (total + limit - 1) // limit if total > 0 else 0
        
        # Return in PaginationResponse format
        return {
            "data": serialize_mongo_doc(result["data"]),
            "meta": {
                "page": page,
                "limit": limit,
                "total": total,
                "total_pages": total_pages,
                "sort_order": sort_order,
                "user_id": str(current_user.user.id)
            }
        }
    except PyMongoError as e:
        loggers.error(f"Database error: {e}")
        raise HTTPException(status_code=500, detail="Database error occurred")
    except Exception as e:
        loggers.error(f"Unexpected error: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
